{"name": "finance-app", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate"}, "dependencies": {}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^2.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "pnpm@10.10.0"}