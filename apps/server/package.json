{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsc && tsc-alias", "check-types": "tsc --noEmit", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/src/index.js", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"dotenv": "^16.4.7", "zod": "^3.25.16", "@orpc/server": "^1.5.0", "@orpc/client": "^1.5.0", "fastify": "^5.3.3", "@fastify/cors": "^11.0.1", "drizzle-orm": "^0.44.2", "pg": "^8.14.1", "better-auth": "^1.2.10"}, "trustedDependencies": ["supabase"], "devDependencies": {"tsc-alias": "^1.8.11", "typescript": "^5.8.2", "tsx": "^4.19.2", "@types/node": "^22.13.11", "drizzle-kit": "^0.31.2", "@types/pg": "^8.11.11"}}