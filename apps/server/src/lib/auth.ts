import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { twoFactor } from "better-auth/plugins";
import { db } from "../db";
import * as schema from "../db/schema/auth";

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: "pg",
		schema: schema,
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || ""],
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: true,
		sendResetPassword: async ({ user, url }) => {
			// TODO: Implement email sending logic
			console.log(`Password reset for ${user.email}: ${url}`);
		},
		sendVerificationEmail: async ({ user, url }) => {
			// TODO: Implement email sending logic
			console.log(`Email verification for ${user.email}: ${url}`);
		},
	},
	socialProviders: {
		google: {
			clientId: process.env.GOOGLE_CLIENT_ID || "",
			clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
		},
		apple: {
			clientId: process.env.APPLE_CLIENT_ID || "",
			clientSecret: process.env.APPLE_CLIENT_SECRET || "",
		},
		github: {
			clientId: process.env.GITHUB_CLIENT_ID || "",
			clientSecret: process.env.GITHUB_CLIENT_SECRET || "",
		},
	},
	plugins: [
		twoFactor({
			issuer: "Finance App",
			otpOptions: {
				period: 30,
				digits: 6,
			},
		}),
	],
	session: {
		expiresIn: 60 * 60 * 24 * 7, // 7 days
		updateAge: 60 * 60 * 24, // 1 day
		cookieCache: {
			enabled: true,
			maxAge: 60 * 5, // 5 minutes
		},
	},
	user: {
		additionalFields: {
			firstName: {
				type: "string",
				required: false,
			},
			lastName: {
				type: "string",
				required: false,
			},
			phoneNumber: {
				type: "string",
				required: false,
			},
			dateOfBirth: {
				type: "date",
				required: false,
			},
			preferredLanguage: {
				type: "string",
				required: false,
				defaultValue: "en",
			},
			timezone: {
				type: "string",
				required: false,
			},
			currency: {
				type: "string",
				required: false,
				defaultValue: "USD",
			},
			notificationPreferences: {
				type: "object",
				required: false,
				defaultValue: {
					email: true,
					push: true,
					sms: false,
					marketing: false,
				},
			},
		},
	},
	secret: process.env.BETTER_AUTH_SECRET,
	baseURL: process.env.BETTER_AUTH_URL,
	advanced: {
		generateId: () => crypto.randomUUID(),
		crossSubDomainCookies: {
			enabled: false,
		},
	},
});
