import { drizzle } from "drizzle-orm/node-postgres";
import * as authSchema from "./schema/auth";
import * as financialSchema from "./schema/financial";
import * as subscriptionSchema from "./schema/subscription";

export const db = drizzle(process.env.DATABASE_URL || "", {
	schema: {
		...authSchema,
		...financialSchema,
		...subscriptionSchema,
	},
});

export { authSchema, financialSchema, subscriptionSchema };
