import { db } from "./index";
import { 
	transactionCategories, 
	subscriptionPlans, 
	featureFlags,
	financialInstitutions 
} from "./schema/financial";
import { defaultSubscriptionPlans } from "./schema/subscription";

// Default transaction categories
const defaultCategories = [
	// Income categories
	{ name: "Salary", icon: "💼", color: "#10B981", isDefault: true },
	{ name: "Freelance", icon: "💻", color: "#10B981", isDefault: true },
	{ name: "Investment Income", icon: "📈", color: "#10B981", isDefault: true },
	{ name: "Other Income", icon: "💰", color: "#10B981", isDefault: true },

	// Expense categories
	{ name: "Food & Dining", icon: "🍽️", color: "#EF4444", isDefault: true },
	{ name: "Transportation", icon: "🚗", color: "#F59E0B", isDefault: true },
	{ name: "Shopping", icon: "🛍️", color: "#8B5CF6", isDefault: true },
	{ name: "Entertainment", icon: "🎬", color: "#EC4899", isDefault: true },
	{ name: "Bills & Utilities", icon: "⚡", color: "#6B7280", isDefault: true },
	{ name: "Healthcare", icon: "🏥", color: "#EF4444", isDefault: true },
	{ name: "Education", icon: "📚", color: "#3B82F6", isDefault: true },
	{ name: "Travel", icon: "✈️", color: "#06B6D4", isDefault: true },
	{ name: "Insurance", icon: "🛡️", color: "#6B7280", isDefault: true },
	{ name: "Taxes", icon: "📋", color: "#6B7280", isDefault: true },
	{ name: "Personal Care", icon: "💅", color: "#F59E0B", isDefault: true },
	{ name: "Gifts & Donations", icon: "🎁", color: "#10B981", isDefault: true },
	{ name: "Home & Garden", icon: "🏠", color: "#84CC16", isDefault: true },
	{ name: "Pets", icon: "🐕", color: "#F59E0B", isDefault: true },
	{ name: "Subscriptions", icon: "📱", color: "#8B5CF6", isDefault: true },
	{ name: "Other Expenses", icon: "📊", color: "#6B7280", isDefault: true },
];

// Subcategories
const subcategories = [
	// Food & Dining subcategories
	{ name: "Restaurants", parentName: "Food & Dining", icon: "🍽️", color: "#EF4444" },
	{ name: "Groceries", parentName: "Food & Dining", icon: "🛒", color: "#EF4444" },
	{ name: "Coffee Shops", parentName: "Food & Dining", icon: "☕", color: "#EF4444" },
	{ name: "Fast Food", parentName: "Food & Dining", icon: "🍔", color: "#EF4444" },

	// Transportation subcategories
	{ name: "Gas", parentName: "Transportation", icon: "⛽", color: "#F59E0B" },
	{ name: "Public Transit", parentName: "Transportation", icon: "🚇", color: "#F59E0B" },
	{ name: "Rideshare", parentName: "Transportation", icon: "🚕", color: "#F59E0B" },
	{ name: "Parking", parentName: "Transportation", icon: "🅿️", color: "#F59E0B" },
	{ name: "Car Maintenance", parentName: "Transportation", icon: "🔧", color: "#F59E0B" },

	// Shopping subcategories
	{ name: "Clothing", parentName: "Shopping", icon: "👕", color: "#8B5CF6" },
	{ name: "Electronics", parentName: "Shopping", icon: "📱", color: "#8B5CF6" },
	{ name: "Books", parentName: "Shopping", icon: "📚", color: "#8B5CF6" },
	{ name: "Home Goods", parentName: "Shopping", icon: "🏠", color: "#8B5CF6" },

	// Bills & Utilities subcategories
	{ name: "Electricity", parentName: "Bills & Utilities", icon: "💡", color: "#6B7280" },
	{ name: "Water", parentName: "Bills & Utilities", icon: "💧", color: "#6B7280" },
	{ name: "Internet", parentName: "Bills & Utilities", icon: "🌐", color: "#6B7280" },
	{ name: "Phone", parentName: "Bills & Utilities", icon: "📞", color: "#6B7280" },
	{ name: "Rent/Mortgage", parentName: "Bills & Utilities", icon: "🏠", color: "#6B7280" },
];

// Popular financial institutions
const defaultInstitutions = [
	// US Banks
	{ name: "Chase Bank", logo: "https://logos.com/chase", website: "https://chase.com", supportedCountries: ["US"] },
	{ name: "Bank of America", logo: "https://logos.com/boa", website: "https://bankofamerica.com", supportedCountries: ["US"] },
	{ name: "Wells Fargo", logo: "https://logos.com/wellsfargo", website: "https://wellsfargo.com", supportedCountries: ["US"] },
	{ name: "Citibank", logo: "https://logos.com/citi", website: "https://citibank.com", supportedCountries: ["US"] },
	{ name: "Capital One", logo: "https://logos.com/capitalone", website: "https://capitalone.com", supportedCountries: ["US"] },
	
	// Canadian Banks
	{ name: "Royal Bank of Canada", logo: "https://logos.com/rbc", website: "https://rbc.com", supportedCountries: ["CA"] },
	{ name: "TD Bank", logo: "https://logos.com/td", website: "https://td.com", supportedCountries: ["CA", "US"] },
	{ name: "Bank of Montreal", logo: "https://logos.com/bmo", website: "https://bmo.com", supportedCountries: ["CA"] },
	
	// Mexican Banks
	{ name: "BBVA México", logo: "https://logos.com/bbva", website: "https://bbva.mx", supportedCountries: ["MX"] },
	{ name: "Santander México", logo: "https://logos.com/santander", website: "https://santander.com.mx", supportedCountries: ["MX"] },
	{ name: "Banorte", logo: "https://logos.com/banorte", website: "https://banorte.com", supportedCountries: ["MX"] },
];

// Default feature flags
const defaultFeatureFlags = [
	{ name: "ai_insights", description: "AI-powered financial insights", isEnabled: true, rolloutPercentage: 100, targetTiers: ["basic", "premium"] },
	{ name: "investment_tracking", description: "Investment portfolio tracking", isEnabled: true, rolloutPercentage: 100, targetTiers: ["premium"] },
	{ name: "advanced_analytics", description: "Advanced spending analytics", isEnabled: true, rolloutPercentage: 100, targetTiers: ["premium"] },
	{ name: "api_access", description: "API access for third-party integrations", isEnabled: true, rolloutPercentage: 100, targetTiers: ["premium"] },
	{ name: "custom_categories", description: "Custom transaction categories", isEnabled: true, rolloutPercentage: 100, targetTiers: ["basic", "premium"] },
	{ name: "bill_reminders", description: "Automated bill reminders", isEnabled: true, rolloutPercentage: 100, targetTiers: ["basic", "premium"] },
	{ name: "goal_tracking", description: "Financial goal tracking", isEnabled: true, rolloutPercentage: 100, targetTiers: ["basic", "premium"] },
	{ name: "data_export", description: "Data export functionality", isEnabled: true, rolloutPercentage: 100, targetTiers: ["basic", "premium"] },
	{ name: "real_time_notifications", description: "Real-time transaction notifications", isEnabled: true, rolloutPercentage: 100, targetTiers: ["premium"] },
	{ name: "multi_currency", description: "Multi-currency support", isEnabled: false, rolloutPercentage: 0, targetTiers: ["premium"] },
];

export async function seedDatabase() {
	console.log("🌱 Starting database seeding...");

	try {
		// Seed subscription plans
		console.log("📋 Seeding subscription plans...");
		for (const plan of defaultSubscriptionPlans) {
			await db.insert(subscriptionPlans).values({
				name: plan.name,
				description: plan.description,
				tier: plan.tier,
				price: plan.price,
				currency: plan.currency,
				billingInterval: plan.billingInterval,
				trialDays: plan.trialDays,
				features: plan.features,
				limits: plan.limits,
				isActive: true,
				sortOrder: plan.tier === "free" ? 0 : plan.tier === "basic" ? 1 : 2,
			}).onConflictDoNothing();
		}

		// Seed transaction categories
		console.log("📊 Seeding transaction categories...");
		const categoryMap = new Map<string, string>();

		// Insert main categories first
		for (const category of defaultCategories) {
			const [inserted] = await db.insert(transactionCategories).values({
				name: category.name,
				icon: category.icon,
				color: category.color,
				isDefault: category.isDefault,
			}).onConflictDoNothing().returning({ id: transactionCategories.id });

			if (inserted) {
				categoryMap.set(category.name, inserted.id);
			}
		}

		// Insert subcategories
		for (const subcategory of subcategories) {
			const parentId = categoryMap.get(subcategory.parentName);
			if (parentId) {
				await db.insert(transactionCategories).values({
					name: subcategory.name,
					parentId: parentId,
					icon: subcategory.icon,
					color: subcategory.color,
					isDefault: true,
				}).onConflictDoNothing();
			}
		}

		// Seed financial institutions
		console.log("🏦 Seeding financial institutions...");
		for (const institution of defaultInstitutions) {
			await db.insert(financialInstitutions).values({
				name: institution.name,
				logo: institution.logo,
				website: institution.website,
				supportedCountries: institution.supportedCountries,
				isActive: true,
			}).onConflictDoNothing();
		}

		// Seed feature flags
		console.log("🚩 Seeding feature flags...");
		for (const flag of defaultFeatureFlags) {
			await db.insert(featureFlags).values({
				name: flag.name,
				description: flag.description,
				isEnabled: flag.isEnabled,
				rolloutPercentage: flag.rolloutPercentage,
				targetTiers: flag.targetTiers,
			}).onConflictDoNothing();
		}

		console.log("✅ Database seeding completed successfully!");
	} catch (error) {
		console.error("❌ Error seeding database:", error);
		throw error;
	}
}

// Run seeding if this file is executed directly
if (import.meta.main) {
	await seedDatabase();
	process.exit(0);
}
