import {
	boolean,
	decimal,
	integer,
	jsonb,
	pgEnum,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { user } from "./auth";

// Enums
export const accountTypeEnum = pgEnum("account_type", [
	"checking",
	"savings",
	"credit_card",
	"investment",
	"loan",
	"mortgage",
	"other",
]);

export const transactionTypeEnum = pgEnum("transaction_type", [
	"income",
	"expense",
	"transfer",
]);

export const transactionStatusEnum = pgEnum("transaction_status", [
	"pending",
	"posted",
	"cancelled",
]);

export const goalTypeEnum = pgEnum("goal_type", [
	"savings",
	"debt_payoff",
	"budget",
	"investment",
]);

export const goalStatusEnum = pgEnum("goal_status", [
	"active",
	"completed",
	"paused",
	"cancelled",
]);

export const budgetPeriodEnum = pgEnum("budget_period", [
	"weekly",
	"monthly",
	"quarterly",
	"yearly",
]);

export const notificationTypeEnum = pgEnum("notification_type", [
	"bill_reminder",
	"budget_alert",
	"goal_milestone",
	"transaction_alert",
	"security_alert",
]);

// Financial Institutions
export const financialInstitutions = pgTable("financial_institutions", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: text("name").notNull(),
	logo: text("logo"),
	website: text("website"),
	plaidInstitutionId: text("plaid_institution_id"),
	belvoInstitutionId: text("belvo_institution_id"),
	supportedCountries: jsonb("supported_countries").default([]),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Bank Accounts
export const bankAccounts = pgTable("bank_accounts", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	institutionId: uuid("institution_id").references(
		() => financialInstitutions.id,
		{ onDelete: "set null" },
	),
	accountType: accountTypeEnum("account_type").notNull(),
	accountName: text("account_name").notNull(),
	accountNumber: text("account_number"), // Encrypted/masked
	routingNumber: text("routing_number"), // Encrypted
	balance: decimal("balance", { precision: 15, scale: 2 }).default("0.00"),
	availableBalance: decimal("available_balance", {
		precision: 15,
		scale: 2,
	}).default("0.00"),
	currency: text("currency").default("USD").notNull(),
	isActive: boolean("is_active").default(true),
	lastSyncAt: timestamp("last_sync_at"),
	plaidAccountId: text("plaid_account_id"),
	belvoAccountId: text("belvo_account_id"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Transaction Categories
export const transactionCategories = pgTable("transaction_categories", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: text("name").notNull(),
	parentId: uuid("parent_id").references(() => transactionCategories.id),
	icon: text("icon"),
	color: text("color"),
	isDefault: boolean("is_default").default(false),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Transactions
export const transactions = pgTable("transactions", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	accountId: uuid("account_id")
		.notNull()
		.references(() => bankAccounts.id, { onDelete: "cascade" }),
	categoryId: uuid("category_id").references(() => transactionCategories.id, {
		onDelete: "set null",
	}),
	type: transactionTypeEnum("type").notNull(),
	status: transactionStatusEnum("status").default("posted"),
	amount: decimal("amount", { precision: 15, scale: 2 }).notNull(),
	currency: text("currency").default("USD").notNull(),
	description: text("description").notNull(),
	merchantName: text("merchant_name"),
	date: timestamp("date").notNull(),
	authorizedDate: timestamp("authorized_date"),
	location: jsonb("location"), // { address, city, region, postal_code, country, lat, lon }
	plaidTransactionId: text("plaid_transaction_id"),
	belvoTransactionId: text("belvo_transaction_id"),
	isRecurring: boolean("is_recurring").default(false),
	recurringGroupId: uuid("recurring_group_id"),
	tags: jsonb("tags").default([]),
	notes: text("notes"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Financial Goals
export const financialGoals = pgTable("financial_goals", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	type: goalTypeEnum("type").notNull(),
	status: goalStatusEnum("status").default("active"),
	name: text("name").notNull(),
	description: text("description"),
	targetAmount: decimal("target_amount", { precision: 15, scale: 2 }),
	currentAmount: decimal("current_amount", {
		precision: 15,
		scale: 2,
	}).default("0.00"),
	currency: text("currency").default("USD").notNull(),
	targetDate: timestamp("target_date"),
	startDate: timestamp("start_date").defaultNow().notNull(),
	completedDate: timestamp("completed_date"),
	linkedAccountId: uuid("linked_account_id").references(() => bankAccounts.id),
	autoContribute: boolean("auto_contribute").default(false),
	contributionAmount: decimal("contribution_amount", {
		precision: 15,
		scale: 2,
	}),
	contributionFrequency: text("contribution_frequency"), // daily, weekly, monthly
	priority: integer("priority").default(1),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Budgets
export const budgets = pgTable("budgets", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	name: text("name").notNull(),
	description: text("description"),
	period: budgetPeriodEnum("period").default("monthly"),
	startDate: timestamp("start_date").notNull(),
	endDate: timestamp("end_date"),
	totalBudget: decimal("total_budget", { precision: 15, scale: 2 }).notNull(),
	totalSpent: decimal("total_spent", {
		precision: 15,
		scale: 2,
	}).default("0.00"),
	currency: text("currency").default("USD").notNull(),
	isActive: boolean("is_active").default(true),
	alertThreshold: integer("alert_threshold").default(80), // Percentage
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Budget Categories (linking budgets to categories with amounts)
export const budgetCategories = pgTable("budget_categories", {
	id: uuid("id").primaryKey().defaultRandom(),
	budgetId: uuid("budget_id")
		.notNull()
		.references(() => budgets.id, { onDelete: "cascade" }),
	categoryId: uuid("category_id")
		.notNull()
		.references(() => transactionCategories.id, { onDelete: "cascade" }),
	allocatedAmount: decimal("allocated_amount", {
		precision: 15,
		scale: 2,
	}).notNull(),
	spentAmount: decimal("spent_amount", {
		precision: 15,
		scale: 2,
	}).default("0.00"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Recurring Transactions/Bills
export const recurringTransactions = pgTable("recurring_transactions", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	accountId: uuid("account_id")
		.notNull()
		.references(() => bankAccounts.id, { onDelete: "cascade" }),
	categoryId: uuid("category_id").references(() => transactionCategories.id),
	name: text("name").notNull(),
	description: text("description"),
	amount: decimal("amount", { precision: 15, scale: 2 }).notNull(),
	currency: text("currency").default("USD").notNull(),
	frequency: text("frequency").notNull(), // daily, weekly, monthly, yearly
	nextDueDate: timestamp("next_due_date").notNull(),
	lastProcessedDate: timestamp("last_processed_date"),
	isActive: boolean("is_active").default(true),
	reminderDays: integer("reminder_days").default(3),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Notifications
export const notifications = pgTable("notifications", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	type: notificationTypeEnum("type").notNull(),
	title: text("title").notNull(),
	message: text("message").notNull(),
	isRead: boolean("is_read").default(false),
	actionUrl: text("action_url"),
	metadata: jsonb("metadata").default({}),
	scheduledFor: timestamp("scheduled_for"),
	sentAt: timestamp("sent_at"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// AI Insights
export const aiInsights = pgTable("ai_insights", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	type: text("type").notNull(), // spending_pattern, anomaly, recommendation, forecast
	title: text("title").notNull(),
	description: text("description").notNull(),
	confidence: decimal("confidence", { precision: 3, scale: 2 }), // 0.00 to 1.00
	data: jsonb("data").notNull(), // Analysis data and charts
	isRead: boolean("is_read").default(false),
	isArchived: boolean("is_archived").default(false),
	validUntil: timestamp("valid_until"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// User Preferences
export const userPreferences = pgTable("user_preferences", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" })
		.unique(),
	defaultCurrency: text("default_currency").default("USD"),
	dateFormat: text("date_format").default("MM/DD/YYYY"),
	numberFormat: text("number_format").default("en-US"),
	budgetStartDay: integer("budget_start_day").default(1), // Day of month
	weekStartDay: integer("week_start_day").default(0), // 0 = Sunday
	enableAiInsights: boolean("enable_ai_insights").default(true),
	enableSpendingAlerts: boolean("enable_spending_alerts").default(true),
	enableBillReminders: boolean("enable_bill_reminders").default(true),
	dataRetentionMonths: integer("data_retention_months").default(24),
	privacySettings: jsonb("privacy_settings").default({
		shareAnonymousData: false,
		enableAnalytics: true,
	}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
