import {
	boolean,
	decimal,
	integer,
	jsonb,
	pgEnum,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { user } from "./auth";

// Enums
export const subscriptionTierEnum = pgEnum("subscription_tier", [
	"free",
	"basic",
	"premium",
	"enterprise",
]);

export const subscriptionStatusEnum = pgEnum("subscription_status", [
	"active",
	"cancelled",
	"past_due",
	"unpaid",
	"trialing",
	"incomplete",
	"incomplete_expired",
]);

export const paymentStatusEnum = pgEnum("payment_status", [
	"pending",
	"succeeded",
	"failed",
	"cancelled",
	"refunded",
]);

export const usageTypeEnum = pgEnum("usage_type", [
	"bank_accounts",
	"transactions",
	"ai_insights",
	"api_calls",
	"data_export",
]);

// Subscription Plans
export const subscriptionPlans = pgTable("subscription_plans", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: text("name").notNull(),
	description: text("description"),
	tier: subscriptionTierEnum("tier").notNull(),
	price: decimal("price", { precision: 10, scale: 2 }).notNull(),
	currency: text("currency").default("USD").notNull(),
	billingInterval: text("billing_interval").notNull(), // month, year
	trialDays: integer("trial_days").default(0),
	features: jsonb("features").notNull(), // List of features included
	limits: jsonb("limits").notNull(), // Usage limits
	stripeProductId: text("stripe_product_id"),
	stripePriceId: text("stripe_price_id"),
	isActive: boolean("is_active").default(true),
	sortOrder: integer("sort_order").default(0),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User Subscriptions
export const userSubscriptions = pgTable("user_subscriptions", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" })
		.unique(),
	planId: uuid("plan_id")
		.notNull()
		.references(() => subscriptionPlans.id),
	status: subscriptionStatusEnum("status").notNull(),
	stripeCustomerId: text("stripe_customer_id"),
	stripeSubscriptionId: text("stripe_subscription_id"),
	currentPeriodStart: timestamp("current_period_start").notNull(),
	currentPeriodEnd: timestamp("current_period_end").notNull(),
	trialStart: timestamp("trial_start"),
	trialEnd: timestamp("trial_end"),
	cancelledAt: timestamp("cancelled_at"),
	cancelAtPeriodEnd: boolean("cancel_at_period_end").default(false),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Payment History
export const payments = pgTable("payments", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	subscriptionId: uuid("subscription_id").references(
		() => userSubscriptions.id,
		{ onDelete: "set null" },
	),
	amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
	currency: text("currency").default("USD").notNull(),
	status: paymentStatusEnum("status").notNull(),
	stripePaymentIntentId: text("stripe_payment_intent_id"),
	stripeChargeId: text("stripe_charge_id"),
	paymentMethod: text("payment_method"), // card, bank_transfer, etc.
	failureReason: text("failure_reason"),
	refundedAmount: decimal("refunded_amount", { precision: 10, scale: 2 }),
	refundedAt: timestamp("refunded_at"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Usage Tracking
export const usageTracking = pgTable("usage_tracking", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	type: usageTypeEnum("type").notNull(),
	count: integer("count").default(1),
	period: text("period").notNull(), // YYYY-MM format for monthly tracking
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Feature Flags
export const featureFlags = pgTable("feature_flags", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: text("name").notNull().unique(),
	description: text("description"),
	isEnabled: boolean("is_enabled").default(false),
	rolloutPercentage: integer("rollout_percentage").default(0), // 0-100
	targetTiers: jsonb("target_tiers").default([]), // Which subscription tiers get this feature
	targetUsers: jsonb("target_users").default([]), // Specific user IDs
	conditions: jsonb("conditions").default({}), // Additional conditions
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User Feature Access
export const userFeatureAccess = pgTable("user_feature_access", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	featureId: uuid("feature_id")
		.notNull()
		.references(() => featureFlags.id, { onDelete: "cascade" }),
	hasAccess: boolean("has_access").notNull(),
	grantedAt: timestamp("granted_at"),
	revokedAt: timestamp("revokedAt"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Billing Events (for webhooks and audit trail)
export const billingEvents = pgTable("billing_events", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id").references(() => user.id, { onDelete: "set null" }),
	eventType: text("event_type").notNull(), // subscription.created, payment.succeeded, etc.
	stripeEventId: text("stripe_event_id"),
	data: jsonb("data").notNull(),
	processed: boolean("processed").default(false),
	processedAt: timestamp("processed_at"),
	errorMessage: text("error_message"),
	retryCount: integer("retry_count").default(0),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Promotional Codes
export const promoCodes = pgTable("promo_codes", {
	id: uuid("id").primaryKey().defaultRandom(),
	code: text("code").notNull().unique(),
	description: text("description"),
	discountType: text("discount_type").notNull(), // percentage, fixed_amount
	discountValue: decimal("discount_value", { precision: 10, scale: 2 }).notNull(),
	currency: text("currency").default("USD"),
	maxUses: integer("max_uses"),
	usedCount: integer("used_count").default(0),
	validFrom: timestamp("valid_from").notNull(),
	validUntil: timestamp("valid_until"),
	applicablePlans: jsonb("applicable_plans").default([]), // Plan IDs this code applies to
	isActive: boolean("is_active").default(true),
	stripePromotionCodeId: text("stripe_promotion_code_id"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Promo Code Usage
export const promoCodeUsage = pgTable("promo_code_usage", {
	id: uuid("id").primaryKey().defaultRandom(),
	promoCodeId: uuid("promo_code_id")
		.notNull()
		.references(() => promoCodes.id, { onDelete: "cascade" }),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	subscriptionId: uuid("subscription_id").references(
		() => userSubscriptions.id,
		{ onDelete: "set null" },
	),
	discountAmount: decimal("discount_amount", { precision: 10, scale: 2 }),
	currency: text("currency").default("USD"),
	usedAt: timestamp("used_at").defaultNow().notNull(),
});

// Default subscription plans data
export const defaultSubscriptionPlans = [
	{
		name: "Free",
		description: "Perfect for getting started with basic financial tracking",
		tier: "free" as const,
		price: "0.00",
		currency: "USD",
		billingInterval: "month",
		trialDays: 0,
		features: [
			"Connect up to 2 bank accounts",
			"Basic transaction categorization",
			"Simple budgeting tools",
			"Monthly spending reports",
			"Email support",
		],
		limits: {
			bankAccounts: 2,
			monthlyTransactions: 500,
			aiInsights: 0,
			dataExport: false,
			apiAccess: false,
		},
	},
	{
		name: "Basic",
		description: "Enhanced features for serious budgeters",
		tier: "basic" as const,
		price: "9.99",
		currency: "USD",
		billingInterval: "month",
		trialDays: 14,
		features: [
			"Connect up to 10 bank accounts",
			"Advanced transaction categorization",
			"Goal tracking and planning",
			"Bill reminders",
			"Weekly AI insights",
			"Priority email support",
		],
		limits: {
			bankAccounts: 10,
			monthlyTransactions: 5000,
			aiInsights: 10,
			dataExport: true,
			apiAccess: false,
		},
	},
	{
		name: "Premium",
		description: "Complete financial management with AI-powered insights",
		tier: "premium" as const,
		price: "19.99",
		currency: "USD",
		billingInterval: "month",
		trialDays: 30,
		features: [
			"Unlimited bank accounts",
			"AI-powered spending analysis",
			"Investment tracking",
			"Advanced goal planning",
			"Custom categories and tags",
			"Real-time notifications",
			"Data export and API access",
			"Priority chat support",
		],
		limits: {
			bankAccounts: -1, // unlimited
			monthlyTransactions: -1, // unlimited
			aiInsights: -1, // unlimited
			dataExport: true,
			apiAccess: true,
		},
	},
] as const;
