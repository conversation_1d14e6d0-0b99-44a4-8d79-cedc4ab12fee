import {
	Configuration,
	PlaidApi,
	PlaidEnvironments,
	Products,
	CountryCode,
	AccountType,
	AccountSubtype,
	TransactionsSyncRequest,
	LinkTokenCreateRequest,
	ItemPublicTokenExchangeRequest,
	AccountsGetRequest,
	TransactionsGetRequest,
	InstitutionsGetByIdRequest,
} from "plaid";
import { db } from "../db";
import {
	bankAccounts,
	transactions,
	financialInstitutions,
} from "../db/schema/financial";
import { eq, and } from "drizzle-orm";

export class PlaidService {
	private client: PlaidApi;

	constructor() {
		const configuration = new Configuration({
			basePath: this.getEnvironment(),
			baseOptions: {
				headers: {
					"PLAID-CLIENT-ID": process.env.PLAID_CLIENT_ID,
					"PLAID-SECRET": process.env.PLAID_SECRET,
				},
			},
		});
		this.client = new PlaidApi(configuration);
	}

	private getEnvironment() {
		const env = process.env.PLAID_ENV || "sandbox";
		switch (env) {
			case "production":
				return PlaidEnvironments.production;
			case "development":
				return PlaidEnvironments.development;
			default:
				return PlaidEnvironments.sandbox;
		}
	}

	/**
	 * Create a link token for Plaid Link initialization
	 */
	async createLinkToken(userId: string, institutionId?: string) {
		try {
			const request: LinkTokenCreateRequest = {
				user: {
					client_user_id: userId,
				},
				client_name: "Finance App",
				products: [Products.Transactions, Products.Auth],
				country_codes: [CountryCode.Us, CountryCode.Ca],
				language: "en",
				webhook: `${process.env.BETTER_AUTH_URL}/api/webhooks/plaid`,
				redirect_uri: `${process.env.CORS_ORIGIN}/dashboard/accounts`,
			};

			if (institutionId) {
				request.institution_id = institutionId;
			}

			const response = await this.client.linkTokenCreate(request);
			return response.data;
		} catch (error) {
			console.error("Error creating link token:", error);
			throw new Error("Failed to create link token");
		}
	}

	/**
	 * Exchange public token for access token
	 */
	async exchangePublicToken(publicToken: string) {
		try {
			const request: ItemPublicTokenExchangeRequest = {
				public_token: publicToken,
			};

			const response = await this.client.itemPublicTokenExchange(request);
			return response.data;
		} catch (error) {
			console.error("Error exchanging public token:", error);
			throw new Error("Failed to exchange public token");
		}
	}

	/**
	 * Get accounts for a user
	 */
	async getAccounts(accessToken: string) {
		try {
			const request: AccountsGetRequest = {
				access_token: accessToken,
			};

			const response = await this.client.accountsGet(request);
			return response.data;
		} catch (error) {
			console.error("Error fetching accounts:", error);
			throw new Error("Failed to fetch accounts");
		}
	}

	/**
	 * Sync accounts to database
	 */
	async syncAccountsToDatabase(
		userId: string,
		accessToken: string,
		itemId: string,
	) {
		try {
			const accountsData = await this.getAccounts(accessToken);

			// Get institution info
			let institutionId: string | null = null;
			if (accountsData.item.institution_id) {
				const institutionRequest: InstitutionsGetByIdRequest = {
					institution_id: accountsData.item.institution_id,
					country_codes: [CountryCode.Us, CountryCode.Ca],
				};

				const institutionResponse =
					await this.client.institutionsGetById(institutionRequest);
				const institution = institutionResponse.data.institution;

				// Upsert institution
				const [existingInstitution] = await db
					.select()
					.from(financialInstitutions)
					.where(eq(financialInstitutions.plaidInstitutionId, institution.institution_id));

				if (!existingInstitution) {
					const [newInstitution] = await db
						.insert(financialInstitutions)
						.values({
							name: institution.name,
							logo: institution.logo || null,
							website: institution.url || null,
							plaidInstitutionId: institution.institution_id,
							supportedCountries: ["US", "CA"],
						})
						.returning({ id: financialInstitutions.id });
					institutionId = newInstitution.id;
				} else {
					institutionId = existingInstitution.id;
				}
			}

			// Sync accounts
			const syncedAccounts = [];
			for (const account of accountsData.accounts) {
				const accountType = this.mapPlaidAccountType(
					account.type,
					account.subtype,
				);

				const [existingAccount] = await db
					.select()
					.from(bankAccounts)
					.where(
						and(
							eq(bankAccounts.userId, userId),
							eq(bankAccounts.plaidAccountId, account.account_id),
						),
					);

				if (existingAccount) {
					// Update existing account
					const [updatedAccount] = await db
						.update(bankAccounts)
						.set({
							balance: account.balances.current?.toString() || "0.00",
							availableBalance:
								account.balances.available?.toString() || "0.00",
							lastSyncAt: new Date(),
							updatedAt: new Date(),
						})
						.where(eq(bankAccounts.id, existingAccount.id))
						.returning();
					syncedAccounts.push(updatedAccount);
				} else {
					// Create new account
					const [newAccount] = await db
						.insert(bankAccounts)
						.values({
							userId,
							institutionId,
							accountType,
							accountName: account.name,
							accountNumber: account.mask || null,
							balance: account.balances.current?.toString() || "0.00",
							availableBalance:
								account.balances.available?.toString() || "0.00",
							currency: account.balances.iso_currency_code || "USD",
							plaidAccountId: account.account_id,
							lastSyncAt: new Date(),
							metadata: {
								itemId,
								officialName: account.official_name,
								persistentAccountId: account.persistent_account_id,
							},
						})
						.returning();
					syncedAccounts.push(newAccount);
				}
			}

			return syncedAccounts;
		} catch (error) {
			console.error("Error syncing accounts:", error);
			throw new Error("Failed to sync accounts");
		}
	}

	/**
	 * Get transactions for an account
	 */
	async getTransactions(
		accessToken: string,
		startDate: string,
		endDate: string,
		accountIds?: string[],
	) {
		try {
			const request: TransactionsGetRequest = {
				access_token: accessToken,
				start_date: startDate,
				end_date: endDate,
				count: 500,
				offset: 0,
			};

			if (accountIds) {
				request.account_ids = accountIds;
			}

			const response = await this.client.transactionsGet(request);
			return response.data;
		} catch (error) {
			console.error("Error fetching transactions:", error);
			throw new Error("Failed to fetch transactions");
		}
	}

	/**
	 * Sync transactions using the sync endpoint (recommended for ongoing sync)
	 */
	async syncTransactions(accessToken: string, cursor?: string) {
		try {
			const request: TransactionsSyncRequest = {
				access_token: accessToken,
			};

			if (cursor) {
				request.cursor = cursor;
			}

			const response = await this.client.transactionsSync(request);
			return response.data;
		} catch (error) {
			console.error("Error syncing transactions:", error);
			throw new Error("Failed to sync transactions");
		}
	}

	/**
	 * Map Plaid account types to our internal types
	 */
	private mapPlaidAccountType(
		type: AccountType,
		subtype: AccountSubtype | null,
	): "checking" | "savings" | "credit_card" | "investment" | "loan" | "mortgage" | "other" {
		switch (type) {
			case AccountType.Depository:
				if (subtype === AccountSubtype.Checking) return "checking";
				if (subtype === AccountSubtype.Savings) return "savings";
				return "checking";
			case AccountType.Credit:
				return "credit_card";
			case AccountType.Investment:
				return "investment";
			case AccountType.Loan:
				if (subtype === AccountSubtype.Mortgage) return "mortgage";
				return "loan";
			default:
				return "other";
		}
	}

	/**
	 * Remove item (disconnect bank)
	 */
	async removeItem(accessToken: string) {
		try {
			await this.client.itemRemove({ access_token: accessToken });
			return true;
		} catch (error) {
			console.error("Error removing item:", error);
			throw new Error("Failed to remove item");
		}
	}

	/**
	 * Get item status
	 */
	async getItemStatus(accessToken: string) {
		try {
			const response = await this.client.itemGet({ access_token: accessToken });
			return response.data;
		} catch (error) {
			console.error("Error getting item status:", error);
			throw new Error("Failed to get item status");
		}
	}
}
