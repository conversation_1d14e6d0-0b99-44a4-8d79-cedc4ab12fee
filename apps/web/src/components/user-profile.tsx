import { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useSession, updateUser, changePassword } from "@/lib/auth-client";
import { toast } from "sonner";

const profileSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().optional(),
  preferredLanguage: z.enum(["en", "es", "fr"]).default("en"),
  timezone: z.string().optional(),
  currency: z.enum(["USD", "EUR", "GBP", "CAD", "MXN"]).default("USD"),
});

const passwordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const notificationSchema = z.object({
  email: z.boolean().default(true),
  push: z.boolean().default(true),
  sms: z.boolean().default(false),
  marketing: z.boolean().default(false),
});

export function UserProfile() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const profileForm = useForm({
    defaultValues: {
      firstName: session?.user?.firstName || "",
      lastName: session?.user?.lastName || "",
      phoneNumber: session?.user?.phoneNumber || "",
      dateOfBirth: session?.user?.dateOfBirth || "",
      preferredLanguage: session?.user?.preferredLanguage || "en",
      timezone: session?.user?.timezone || "",
      currency: session?.user?.currency || "USD",
    },
    onSubmit: async ({ value }) => {
      setIsLoading(true);
      try {
        await updateUser(value);
        toast.success("Profile updated successfully");
      } catch (error) {
        toast.error("Failed to update profile");
      } finally {
        setIsLoading(false);
      }
    },
    validatorAdapter: zodValidator(),
    validators: {
      onChange: profileSchema,
    },
  });

  const passwordForm = useForm({
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    onSubmit: async ({ value }) => {
      setIsLoading(true);
      try {
        await changePassword({
          currentPassword: value.currentPassword,
          newPassword: value.newPassword,
        });
        toast.success("Password changed successfully");
        passwordForm.reset();
      } catch (error) {
        toast.error("Failed to change password");
      } finally {
        setIsLoading(false);
      }
    },
    validatorAdapter: zodValidator(),
    validators: {
      onChange: passwordSchema,
    },
  });

  const notificationForm = useForm({
    defaultValues: session?.user?.notificationPreferences || {
      email: true,
      push: true,
      sms: false,
      marketing: false,
    },
    onSubmit: async ({ value }) => {
      setIsLoading(true);
      try {
        await updateUser({ notificationPreferences: value });
        toast.success("Notification preferences updated");
      } catch (error) {
        toast.error("Failed to update preferences");
      } finally {
        setIsLoading(false);
      }
    },
    validatorAdapter: zodValidator(),
    validators: {
      onChange: notificationSchema,
    },
  });

  if (!session) {
    return <div>Please sign in to view your profile.</div>;
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">User Profile</h1>
      
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update your personal details and contact information.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  profileForm.handleSubmit();
                }}
                className="space-y-4"
              >
                <div className="grid grid-cols-2 gap-4">
                  <profileForm.Field name="firstName">
                    {(field) => (
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Enter your first name"
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-red-500 mt-1">
                            {field.state.meta.errors[0]}
                          </p>
                        )}
                      </div>
                    )}
                  </profileForm.Field>

                  <profileForm.Field name="lastName">
                    {(field) => (
                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Enter your last name"
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-red-500 mt-1">
                            {field.state.meta.errors[0]}
                          </p>
                        )}
                      </div>
                    )}
                  </profileForm.Field>
                </div>

                <profileForm.Field name="phoneNumber">
                  {(field) => (
                    <div>
                      <Label htmlFor="phoneNumber">Phone Number</Label>
                      <Input
                        id="phoneNumber"
                        type="tel"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter your phone number"
                      />
                      {field.state.meta.errors && (
                        <p className="text-sm text-red-500 mt-1">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                </profileForm.Field>

                <profileForm.Field name="dateOfBirth">
                  {(field) => (
                    <div>
                      <Label htmlFor="dateOfBirth">Date of Birth</Label>
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                      />
                      {field.state.meta.errors && (
                        <p className="text-sm text-red-500 mt-1">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                </profileForm.Field>

                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Updating..." : "Update Profile"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your password and two-factor authentication.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  passwordForm.handleSubmit();
                }}
                className="space-y-4"
              >
                <passwordForm.Field name="currentPassword">
                  {(field) => (
                    <div>
                      <Label htmlFor="currentPassword">Current Password</Label>
                      <Input
                        id="currentPassword"
                        type="password"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter your current password"
                      />
                      {field.state.meta.errors && (
                        <p className="text-sm text-red-500 mt-1">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                </passwordForm.Field>

                <passwordForm.Field name="newPassword">
                  {(field) => (
                    <div>
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter your new password"
                      />
                      {field.state.meta.errors && (
                        <p className="text-sm text-red-500 mt-1">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                </passwordForm.Field>

                <passwordForm.Field name="confirmPassword">
                  {(field) => (
                    <div>
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Confirm your new password"
                      />
                      {field.state.meta.errors && (
                        <p className="text-sm text-red-500 mt-1">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                </passwordForm.Field>

                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Changing..." : "Change Password"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Choose how you want to receive notifications.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  notificationForm.handleSubmit();
                }}
                className="space-y-4"
              >
                <notificationForm.Field name="email">
                  {(field) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email">Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive notifications via email
                        </p>
                      </div>
                      <Switch
                        id="email"
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                    </div>
                  )}
                </notificationForm.Field>

                <notificationForm.Field name="push">
                  {(field) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="push">Push Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive push notifications in your browser
                        </p>
                      </div>
                      <Switch
                        id="push"
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                    </div>
                  )}
                </notificationForm.Field>

                <notificationForm.Field name="sms">
                  {(field) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="sms">SMS Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive notifications via SMS
                        </p>
                      </div>
                      <Switch
                        id="sms"
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                    </div>
                  )}
                </notificationForm.Field>

                <notificationForm.Field name="marketing">
                  {(field) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="marketing">Marketing Communications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive marketing emails and updates
                        </p>
                      </div>
                      <Switch
                        id="marketing"
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                    </div>
                  )}
                </notificationForm.Field>

                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Updating..." : "Update Preferences"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>App Preferences</CardTitle>
              <CardDescription>
                Customize your app experience.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <profileForm.Field name="preferredLanguage">
                  {(field) => (
                    <div>
                      <Label htmlFor="preferredLanguage">Preferred Language</Label>
                      <Select value={field.state.value} onValueChange={field.handleChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Español</SelectItem>
                          <SelectItem value="fr">Français</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </profileForm.Field>

                <profileForm.Field name="currency">
                  {(field) => (
                    <div>
                      <Label htmlFor="currency">Default Currency</Label>
                      <Select value={field.state.value} onValueChange={field.handleChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD - US Dollar</SelectItem>
                          <SelectItem value="EUR">EUR - Euro</SelectItem>
                          <SelectItem value="GBP">GBP - British Pound</SelectItem>
                          <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                          <SelectItem value="MXN">MXN - Mexican Peso</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </profileForm.Field>

                <profileForm.Field name="timezone">
                  {(field) => (
                    <div>
                      <Label htmlFor="timezone">Timezone</Label>
                      <Input
                        id="timezone"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="e.g., America/New_York"
                      />
                    </div>
                  )}
                </profileForm.Field>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
