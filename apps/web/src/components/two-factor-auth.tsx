import { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  enableTwoFactor, 
  disableTwoFactor, 
  verifyTwoFactor, 
  getTwoFactorBackupCodes,
  useSession 
} from "@/lib/auth-client";
import { toast } from "sonner";
import { Shield, ShieldCheck, Copy, Eye, EyeOff } from "lucide-react";

const verificationSchema = z.object({
  code: z.string().min(6, "Code must be 6 digits").max(6, "Code must be 6 digits"),
});

interface TwoFactorAuthProps {
  onComplete?: () => void;
}

export function TwoFactorAuth({ onComplete }: TwoFactorAuthProps) {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [qrCode, setQrCode] = useState<string>("");
  const [secret, setSecret] = useState<string>("");
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [step, setStep] = useState<"setup" | "verify" | "complete">("setup");

  const verificationForm = useForm({
    defaultValues: {
      code: "",
    },
    onSubmit: async ({ value }) => {
      setIsLoading(true);
      try {
        await verifyTwoFactor({ code: value.code });
        toast.success("Two-factor authentication enabled successfully");
        setStep("complete");
        onComplete?.();
      } catch (error) {
        toast.error("Invalid verification code");
      } finally {
        setIsLoading(false);
      }
    },
    validatorAdapter: zodValidator(),
    validators: {
      onChange: verificationSchema,
    },
  });

  const handleEnableTwoFactor = async () => {
    setIsLoading(true);
    try {
      const result = await enableTwoFactor();
      setQrCode(result.qrCode);
      setSecret(result.secret);
      setStep("verify");
    } catch (error) {
      toast.error("Failed to enable two-factor authentication");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisableTwoFactor = async () => {
    setIsLoading(true);
    try {
      await disableTwoFactor();
      toast.success("Two-factor authentication disabled");
      setStep("setup");
      setQrCode("");
      setSecret("");
      setBackupCodes([]);
    } catch (error) {
      toast.error("Failed to disable two-factor authentication");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGetBackupCodes = async () => {
    setIsLoading(true);
    try {
      const codes = await getTwoFactorBackupCodes();
      setBackupCodes(codes);
      setShowBackupCodes(true);
    } catch (error) {
      toast.error("Failed to generate backup codes");
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const copyAllBackupCodes = () => {
    const codesText = backupCodes.join("\n");
    navigator.clipboard.writeText(codesText);
    toast.success("All backup codes copied to clipboard");
  };

  if (!session) {
    return <div>Please sign in to manage two-factor authentication.</div>;
  }

  const isTwoFactorEnabled = session.user?.twoFactorEnabled;

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isTwoFactorEnabled ? (
              <ShieldCheck className="h-5 w-5 text-green-600" />
            ) : (
              <Shield className="h-5 w-5 text-gray-400" />
            )}
            Two-Factor Authentication
          </CardTitle>
          <CardDescription>
            Add an extra layer of security to your account with two-factor authentication.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isTwoFactorEnabled ? (
            <div className="space-y-4">
              <Alert>
                <ShieldCheck className="h-4 w-4" />
                <AlertDescription>
                  Two-factor authentication is currently <Badge variant="secondary" className="ml-1">Enabled</Badge>
                </AlertDescription>
              </Alert>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={handleGetBackupCodes}
                  disabled={isLoading}
                >
                  Generate Backup Codes
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={handleDisableTwoFactor}
                  disabled={isLoading}
                >
                  {isLoading ? "Disabling..." : "Disable 2FA"}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {step === "setup" && (
                <div className="space-y-4">
                  <Alert>
                    <Shield className="h-4 w-4" />
                    <AlertDescription>
                      Two-factor authentication is currently <Badge variant="outline" className="ml-1">Disabled</Badge>
                    </AlertDescription>
                  </Alert>
                  
                  <p className="text-sm text-muted-foreground">
                    Enable two-factor authentication to secure your account with an authenticator app like 
                    Google Authenticator, Authy, or 1Password.
                  </p>
                  
                  <Button onClick={handleEnableTwoFactor} disabled={isLoading}>
                    {isLoading ? "Setting up..." : "Enable Two-Factor Authentication"}
                  </Button>
                </div>
              )}

              {step === "verify" && (
                <div className="space-y-4">
                  <div className="text-center space-y-4">
                    <h3 className="text-lg font-semibold">Scan QR Code</h3>
                    <p className="text-sm text-muted-foreground">
                      Scan this QR code with your authenticator app, then enter the verification code below.
                    </p>
                    
                    {qrCode && (
                      <div className="flex justify-center">
                        <img src={qrCode} alt="QR Code" className="border rounded-lg" />
                      </div>
                    )}
                    
                    {secret && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Manual Entry Key:</p>
                        <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
                          <code className="text-sm flex-1">{secret}</code>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(secret)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      verificationForm.handleSubmit();
                    }}
                    className="space-y-4"
                  >
                    <verificationForm.Field name="code">
                      {(field) => (
                        <div>
                          <Label htmlFor="code">Verification Code</Label>
                          <Input
                            id="code"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="Enter 6-digit code"
                            maxLength={6}
                            className="text-center text-lg tracking-widest"
                          />
                          {field.state.meta.errors && (
                            <p className="text-sm text-red-500 mt-1">
                              {field.state.meta.errors[0]}
                            </p>
                          )}
                        </div>
                      )}
                    </verificationForm.Field>

                    <div className="flex gap-2">
                      <Button type="submit" disabled={isLoading}>
                        {isLoading ? "Verifying..." : "Verify & Enable"}
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setStep("setup")}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </div>
              )}

              {step === "complete" && (
                <div className="text-center space-y-4">
                  <ShieldCheck className="h-12 w-12 text-green-600 mx-auto" />
                  <h3 className="text-lg font-semibold">Two-Factor Authentication Enabled!</h3>
                  <p className="text-sm text-muted-foreground">
                    Your account is now protected with two-factor authentication.
                  </p>
                  <Button onClick={handleGetBackupCodes}>
                    Generate Backup Codes
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {showBackupCodes && backupCodes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Backup Codes
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowBackupCodes(!showBackupCodes)}
              >
                {showBackupCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </CardTitle>
            <CardDescription>
              Save these backup codes in a secure location. Each code can only be used once.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <AlertDescription>
                <strong>Important:</strong> Store these codes securely. If you lose access to your 
                authenticator app, you can use these codes to regain access to your account.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                {backupCodes.map((code, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                    <code className="text-sm flex-1">{code}</code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(code)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              
              <Button onClick={copyAllBackupCodes} className="w-full">
                Copy All Codes
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
