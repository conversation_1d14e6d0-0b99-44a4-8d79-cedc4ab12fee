import { useForm } from "@tanstack/react-form";
import { useNavigate } from "@tanstack/react-router";
import { Github, Mail } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import z from "zod/v4";
import {
	authClient,
	resetPassword,
	signInWithProvider,
} from "@/lib/auth-client";
import Loader from "./loader";
import { Button } from "./ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Separator } from "./ui/separator";

export default function SignInForm({
	onSwitchToSignUp,
}: {
	onSwitchToSignUp: () => void;
}) {
	const navigate = useNavigate({
		from: "/",
	});
	const { isPending } = authClient.useSession();
	const [showForgotPassword, setShowForgotPassword] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	const form = useForm({
		defaultValues: {
			email: "",
			password: "",
		},
		onSubmit: async ({ value }) => {
			await authClient.signIn.email(
				{
					email: value.email,
					password: value.password,
				},
				{
					onSuccess: () => {
						navigate({
							to: "/dashboard",
						});
						toast.success("Sign in successful");
					},
					onError: (error) => {
						toast.error(error.error.message);
					},
				},
			);
		},
		validators: {
			onSubmit: z.object({
				email: z.email("Invalid email address"),
				password: z.string().min(8, "Password must be at least 8 characters"),
			}),
		},
	});

	const forgotPasswordForm = useForm({
		defaultValues: {
			email: "",
		},
		onSubmit: async ({ value }) => {
			setIsLoading(true);
			try {
				await resetPassword({ email: value.email });
				toast.success("Password reset email sent");
				setShowForgotPassword(false);
			} catch (error) {
				toast.error("Failed to send password reset email");
			} finally {
				setIsLoading(false);
			}
		},
		validators: {
			onSubmit: z.object({
				email: z.email("Invalid email address"),
			}),
		},
	});

	const handleSocialSignIn = async (
		provider: "google" | "github" | "apple",
	) => {
		setIsLoading(true);
		try {
			await signInWithProvider(provider, {
				callbackURL: "/dashboard",
			});
		} catch (error) {
			toast.error(`Failed to sign in with ${provider}`);
		} finally {
			setIsLoading(false);
		}
	};

	if (isPending) {
		return <Loader />;
	}

	if (showForgotPassword) {
		return (
			<div className="mx-auto mt-10 w-full max-w-md p-6">
				<Card>
					<CardHeader>
						<CardTitle>Reset Password</CardTitle>
						<CardDescription>
							Enter your email address and we'll send you a link to reset your
							password.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<form
							onSubmit={(e) => {
								e.preventDefault();
								e.stopPropagation();
								void forgotPasswordForm.handleSubmit();
							}}
							className="space-y-4"
						>
							<forgotPasswordForm.Field name="email">
								{(field) => (
									<div className="space-y-2">
										<Label htmlFor={field.name}>Email</Label>
										<Input
											id={field.name}
											name={field.name}
											type="email"
											value={field.state.value}
											onBlur={field.handleBlur}
											onChange={(e) => field.handleChange(e.target.value)}
											placeholder="Enter your email"
										/>
										{field.state.meta.errors.map((error) => (
											<p key={error?.message} className="text-red-500 text-sm">
												{error?.message}
											</p>
										))}
									</div>
								)}
							</forgotPasswordForm.Field>

							<div className="flex gap-2">
								<Button type="submit" disabled={isLoading} className="flex-1">
									{isLoading ? "Sending..." : "Send Reset Link"}
								</Button>
								<Button
									type="button"
									variant="outline"
									onClick={() => setShowForgotPassword(false)}
								>
									Cancel
								</Button>
							</div>
						</form>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="mx-auto mt-10 w-full max-w-md p-6">
			<Card>
				<CardHeader>
					<CardTitle className="text-center text-2xl">Welcome Back</CardTitle>
					<CardDescription className="text-center">
						Sign in to your Finance App account
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Social Sign In Buttons */}
					<div className="space-y-3">
						<Button
							variant="outline"
							className="w-full"
							onClick={() => handleSocialSignIn("google")}
							disabled={isLoading}
						>
							<Mail className="mr-2 h-4 w-4" />
							Continue with Google
						</Button>
						<Button
							variant="outline"
							className="w-full"
							onClick={() => handleSocialSignIn("github")}
							disabled={isLoading}
						>
							<Github className="mr-2 h-4 w-4" />
							Continue with GitHub
						</Button>
					</div>

					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<Separator className="w-full" />
						</div>
						<div className="relative flex justify-center text-xs uppercase">
							<span className="bg-background px-2 text-muted-foreground">
								Or continue with email
							</span>
						</div>
					</div>

					{/* Email/Password Form */}
					<form
						onSubmit={(e) => {
							e.preventDefault();
							e.stopPropagation();
							void form.handleSubmit();
						}}
						className="space-y-4"
					>
						<form.Field name="email">
							{(field) => (
								<div className="space-y-2">
									<Label htmlFor={field.name}>Email</Label>
									<Input
										id={field.name}
										name={field.name}
										type="email"
										value={field.state.value}
										onBlur={field.handleBlur}
										onChange={(e) => field.handleChange(e.target.value)}
										placeholder="Enter your email"
									/>
									{field.state.meta.errors.map((error) => (
										<p key={error?.message} className="text-red-500 text-sm">
											{error?.message}
										</p>
									))}
								</div>
							)}
						</form.Field>

						<form.Field name="password">
							{(field) => (
								<div className="space-y-2">
									<div className="flex items-center justify-between">
										<Label htmlFor={field.name}>Password</Label>
										<Button
											variant="link"
											size="sm"
											onClick={() => setShowForgotPassword(true)}
											className="px-0 font-normal"
										>
											Forgot password?
										</Button>
									</div>
									<Input
										id={field.name}
										name={field.name}
										type="password"
										value={field.state.value}
										onBlur={field.handleBlur}
										onChange={(e) => field.handleChange(e.target.value)}
										placeholder="Enter your password"
									/>
									{field.state.meta.errors.map((error) => (
										<p key={error?.message} className="text-red-500 text-sm">
											{error?.message}
										</p>
									))}
								</div>
							)}
						</form.Field>

						<form.Subscribe>
							{(state) => (
								<Button
									type="submit"
									className="w-full"
									disabled={!state.canSubmit || state.isSubmitting || isLoading}
								>
									{state.isSubmitting ? "Signing in..." : "Sign In"}
								</Button>
							)}
						</form.Subscribe>
					</form>

					<div className="text-center">
						<Button
							variant="link"
							onClick={onSwitchToSignUp}
							className="text-sm"
						>
							Don't have an account? Sign up
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
