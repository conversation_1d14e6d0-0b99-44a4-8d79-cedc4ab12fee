import { twoFactorClient } from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

export const authClient = createAuthClient({
	baseURL: import.meta.env.VITE_SERVER_URL,
	plugins: [twoFactorClient()],
});

export const {
	signIn,
	signUp,
	signOut,
	useSession,
	getSession,
	updateUser,
	changePassword,
	resetPassword,
	sendVerificationEmail,
	verifyEmail,
	// Two-factor authentication
	enableTwoFactor,
	disableTwoFactor,
	verifyTwoFactor,
	getTwoFactorBackupCodes,
	// Social auth
	signInWithProvider,
} = authClient;
